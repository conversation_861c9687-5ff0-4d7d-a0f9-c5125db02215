#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы групп учителя
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import TeacherRepository, UserRepository

async def test_teacher_groups():
    """Тестируем получение групп учителя"""
    
    # Тестовый telegram_id учителя (замените на реальный)
    test_telegram_id = 955518340  # ankv123_test
    
    print(f"🔍 Тестируем получение групп для учителя с telegram_id: {test_telegram_id}")
    
    try:
        # Получаем пользователя по telegram_id
        user = await UserRepository.get_by_telegram_id(test_telegram_id)
        print(f"👤 Пользователь: {user.name if user else 'НЕ НАЙДЕН'}")
        
        if user:
            # Получаем профиль учителя
            teacher = await TeacherRepository.get_by_user_id(user.id)
            print(f"👨‍🏫 Учитель: {'ID=' + str(teacher.id) if teacher else 'НЕ НАЙДЕН'}")
            
            if teacher:
                # Получаем группы учителя
                groups = await TeacherRepository.get_teacher_groups(teacher.id)
                print(f"📚 Найдено групп: {len(groups)}")
                
                for group in groups:
                    subject_name = group.subject.name if group.subject else "Без предмета"
                    print(f"  - {group.name} ({subject_name})")
                    
                return len(groups) > 0
            else:
                print("❌ Пользователь не является учителем")
                return False
        else:
            print("❌ Пользователь не найден")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_teacher_groups())
    if result:
        print("✅ Тест пройден успешно!")
    else:
        print("❌ Тест не пройден!")
